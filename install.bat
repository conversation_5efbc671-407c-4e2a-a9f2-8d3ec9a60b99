@echo off
chcp 65001 >nul
echo ===============================================
echo    تثبيت نظام مراقبة الإعلام
echo ===============================================
echo.

echo 🔄 جاري التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 📝 يرجى تثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python مثبت
echo.

echo 🔄 جاري التحقق من pip...
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    pause
    exit /b 1
)

echo ✅ pip متوفر
echo.

echo 🔄 جاري تحديث pip...
python -m pip install --upgrade pip

echo.
echo 🔄 جاري تشغيل إعداد المشروع...
python setup.py

echo.
echo 📝 للتحقق من تثبيت Node.js و Firebase CLI:
echo.
echo node --version
node --version 2>nul || echo ❌ Node.js غير مثبت - حمله من: https://nodejs.org

echo.
echo npm --version  
npm --version 2>nul || echo ❌ npm غير متوفر

echo.
echo firebase --version
firebase --version 2>nul || echo ❌ Firebase CLI غير مثبت - قم بتشغيل: npm install -g firebase-tools

echo.
echo ===============================================
echo    انتهى التثبيت
echo ===============================================
echo.
echo 📝 لتشغيل التطبيق: python run.py
echo 📝 لنشر على Firebase: اتبع تعليمات README_FIREBASE.md
echo.
pause
