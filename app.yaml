# تكوين Google App Engine لنظام مراقبة الإعلام
runtime: python311

# إعدادات التطبيق
env_variables:
  SECRET_KEY: "your-secret-key-here"
  FLASK_ENV: "production"
  
# إعدادات الموارد
automatic_scaling:
  min_instances: 0
  max_instances: 10
  target_cpu_utilization: 0.6

# معالجات الملفات الثابتة
handlers:
  # ملفات CSS
  - url: /static/css
    static_dir: app/static/css
    secure: always
    
  # ملفات JavaScript
  - url: /static/js
    static_dir: app/static/js
    secure: always
    
  # الصور
  - url: /static/img
    static_dir: app/static/img
    secure: always
    
  # الخطوط
  - url: /static/fonts
    static_dir: app/static/fonts
    secure: always
    
  # جميع الملفات الثابتة الأخرى
  - url: /static
    static_dir: app/static
    secure: always
    
  # التطبيق الرئيسي
  - url: /.*
    script: auto
    secure: always

# الملفات المستبعدة من النشر
skip_files:
  - ^(.*/)?#.*#$
  - ^(.*/)?.*~$
  - ^(.*/)?.*\.py[co]$
  - ^(.*/)?.*/RCS/.*$
  - ^(.*/)?\..*$
  - ^(.*/)?venv/.*$
  - ^(.*/)?__pycache__/.*$
  - ^(.*/)?backups/.*$
  - ^(.*/)?installer/.*$
  - ^(.*/)?\.git/.*$
  - ^(.*/)?node_modules/.*$
