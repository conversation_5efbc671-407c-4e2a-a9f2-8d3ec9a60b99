#!/bin/bash

# سكريبت نشر نظام مراقبة الإعلام
# يدعم Firebase, App Engine, Heroku

echo "🚀 مرحباً بك في سكريبت نشر نظام مراقبة الإعلام"
echo "=================================================="

# التحقق من وجود ملف requirements.txt
if [ ! -f "requirements.txt" ]; then
    echo "❌ ملف requirements.txt غير موجود"
    exit 1
fi

# عرض خيارات النشر
echo "اختر منصة النشر:"
echo "1) Firebase Functions + Hosting"
echo "2) Google App Engine"
echo "3) Heroku"
echo "4) اختبار محلي"
echo ""
read -p "أدخل رقم الخيار (1-4): " choice

case $choice in
    1)
        echo "🔄 جاري النشر على Firebase..."
        
        # التحقق من تسجيل الدخول
        if ! firebase projects:list >/dev/null 2>&1; then
            echo "🔑 يرجى تسجيل الدخول إلى Firebase"
            firebase login
        fi
        
        # التحقق من ربط المشروع
        if [ ! -f ".firebaserc" ]; then
            echo "🔗 يرجى ربط المشروع بـ Firebase"
            firebase use --add
        fi
        
        # النشر
        echo "📦 جاري نشر Functions و Hosting..."
        firebase deploy
        
        if [ $? -eq 0 ]; then
            echo "✅ تم النشر بنجاح على Firebase!"
            echo "🌐 يمكنك الوصول للموقع من خلال الرابط المعروض أعلاه"
        else
            echo "❌ فشل النشر على Firebase"
        fi
        ;;
        
    2)
        echo "🔄 جاري النشر على Google App Engine..."
        
        # التحقق من تسجيل الدخول
        if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n 1 >/dev/null 2>&1; then
            echo "🔑 يرجى تسجيل الدخول إلى Google Cloud"
            gcloud auth login
        fi
        
        # التحقق من تحديد المشروع
        if [ -z "$(gcloud config get-value project)" ]; then
            echo "🔗 يرجى تحديد مشروع Google Cloud"
            gcloud projects list
            read -p "أدخل معرف المشروع: " project_id
            gcloud config set project $project_id
        fi
        
        # النشر
        echo "📦 جاري النشر على App Engine..."
        gcloud app deploy
        
        if [ $? -eq 0 ]; then
            echo "✅ تم النشر بنجاح على App Engine!"
            echo "🌐 جاري فتح الموقع..."
            gcloud app browse
        else
            echo "❌ فشل النشر على App Engine"
        fi
        ;;
        
    3)
        echo "🔄 جاري النشر على Heroku..."
        
        # التحقق من تسجيل الدخول
        if ! heroku auth:whoami >/dev/null 2>&1; then
            echo "🔑 يرجى تسجيل الدخول إلى Heroku"
            heroku login
        fi
        
        # التحقق من وجود تطبيق Heroku
        if ! heroku apps:info >/dev/null 2>&1; then
            echo "📱 إنشاء تطبيق Heroku جديد..."
            read -p "أدخل اسم التطبيق (أو اتركه فارغاً للاسم التلقائي): " app_name
            if [ -z "$app_name" ]; then
                heroku create
            else
                heroku create $app_name
            fi
        fi
        
        # تكوين متغيرات البيئة
        echo "🔧 تكوين متغيرات البيئة..."
        heroku config:set FLASK_ENV=production
        
        # إنشاء مفتاح أمان عشوائي إذا لم يكن موجوداً
        if [ -z "$(heroku config:get SECRET_KEY)" ]; then
            secret_key=$(python -c "import secrets; print(secrets.token_hex(32))")
            heroku config:set SECRET_KEY="$secret_key"
        fi
        
        # النشر
        echo "📦 جاري النشر على Heroku..."
        git add .
        git commit -m "Deploy to Heroku - $(date)"
        git push heroku main
        
        if [ $? -eq 0 ]; then
            echo "✅ تم النشر بنجاح على Heroku!"
            echo "🌐 جاري فتح الموقع..."
            heroku open
        else
            echo "❌ فشل النشر على Heroku"
        fi
        ;;
        
    4)
        echo "🔄 جاري تشغيل الاختبار المحلي..."
        
        # تثبيت المتطلبات
        echo "📦 تثبيت المتطلبات..."
        pip install -r requirements.txt
        
        # تشغيل التطبيق
        echo "🌐 تشغيل التطبيق على http://127.0.0.1:5000"
        python wsgi.py
        ;;
        
    *)
        echo "❌ خيار غير صحيح"
        exit 1
        ;;
esac

echo ""
echo "=================================================="
echo "🎉 انتهت عملية النشر"
echo "📝 للمزيد من المعلومات، راجع README_FIREBASE.md"
