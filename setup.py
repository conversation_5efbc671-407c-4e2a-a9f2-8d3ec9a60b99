#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف الإعداد لنظام مراقبة الإعلام
يقوم بتثبيت جميع المتطلبات وتحضير البيئة
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def install_requirements():
    """تثبيت متطلبات Python"""
    print("🔄 جاري تثبيت متطلبات Python...")
    
    try:
        # تحديث pip أولاً
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # تثبيت المتطلبات من ملف requirements.txt
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ تم تثبيت جميع المتطلبات بنجاح!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def create_env_file():
    """إنشاء ملف .env إذا لم يكن موجوداً"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("🔄 جاري إنشاء ملف .env...")
        
        # نسخ محتوى .env.example إلى .env
        with open(env_example, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إنشاء مفتاح أمان عشوائي
        import secrets
        secret_key = secrets.token_hex(32)
        content = content.replace('your-secret-key-here', secret_key)
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إنشاء ملف .env بنجاح!")
    else:
        print("ℹ️ ملف .env موجود بالفعل")

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🔄 جاري إعداد قاعدة البيانات...")
    
    try:
        # استيراد التطبيق وإنشاء قاعدة البيانات
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app import app, db
        
        with app.app_context():
            # إنشاء جميع الجداول
            db.create_all()
            
            # تهيئة البيانات الأساسية
            from app.models.relational_models import init_governorates
            init_governorates(db.session)
            
        print("✅ تم إعداد قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def check_firebase_cli():
    """التحقق من تثبيت Firebase CLI"""
    print("🔄 جاري التحقق من Firebase CLI...")
    
    try:
        result = subprocess.run(['firebase', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Firebase CLI مثبت: {result.stdout.strip()}")
        return True
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Firebase CLI غير مثبت")
        print("📝 لتثبيت Firebase CLI، قم بتشغيل:")
        print("   npm install -g firebase-tools")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("🔄 جاري إنشاء المجلدات المطلوبة...")
    
    directories = [
        'backups',
        'app/static/uploads',
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ تم إنشاء جميع المجلدات المطلوبة!")

def main():
    """الدالة الرئيسية للإعداد"""
    print("=" * 60)
    print("🚀 مرحباً بك في إعداد نظام مراقبة الإعلام")
    print("=" * 60)
    
    # التحقق من إصدار Python
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        sys.exit(1)
    
    print(f"✅ إصدار Python: {sys.version}")
    
    # تنفيذ خطوات الإعداد
    steps = [
        ("تثبيت متطلبات Python", install_requirements),
        ("إنشاء ملف البيئة", create_env_file),
        ("إنشاء المجلدات", create_directories),
        ("إعداد قاعدة البيانات", setup_database),
        ("التحقق من Firebase CLI", check_firebase_cli)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if step_func():
            success_count += 1
    
    print("\n" + "=" * 60)
    if success_count == len(steps):
        print("🎉 تم إعداد المشروع بنجاح!")
        print("\n📝 الخطوات التالية:")
        print("1. قم بتشغيل: python run.py")
        print("2. افتح المتصفح على: http://127.0.0.1:5000")
        print("3. لنشر على Firebase، اتبع تعليمات README_FIREBASE.md")
    else:
        print(f"⚠️ تم إكمال {success_count} من {len(steps)} خطوات")
        print("📝 يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
