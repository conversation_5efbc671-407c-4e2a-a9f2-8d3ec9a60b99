import cv2
import numpy as np
from PIL import Image

# Read image using OpenCV
img = cv2.imread('app/static/img/logo2.png', cv2.IMREAD_UNCHANGED)

# Convert BGR to BGRA if necessary
if img.shape[2] == 3:
    img = cv2.cvtColor(img, cv2.COLOR_BGR2BGRA)

# Resize to 32x32 (standard icon size)
img = cv2.resize(img, (32, 32), interpolation=cv2.INTER_LANCZOS4)

# Convert to PIL Image
img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGRA2RGBA))

# Save as ICO
img_pil.save('logo.ico', format='ICO', sizes=[(32, 32)])
