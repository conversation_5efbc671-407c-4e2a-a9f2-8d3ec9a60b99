#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف WSGI لتشغيل نظام مراقبة الإعلام على خوادم الويب
يدعم Gunicorn, uWSGI, وخوادم WSGI الأخرى
"""

import os
import sys
import logging

# إضافة المجلد الحالي إلى مسار البحث
sys.path.insert(0, os.path.dirname(__file__))

# تكوين التسجيل للإنتاج
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(name)s %(message)s'
)

# استيراد التطبيق
from app import app

# تكوين التطبيق للإنتاج
app.config['DEBUG'] = False
app.config['TESTING'] = False

# تكوين قاعدة البيانات للإنتاج
if os.environ.get('DATABASE_URL'):
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL')
elif os.environ.get('GOOGLE_CLOUD_PROJECT'):
    # في بيئة Google Cloud
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///tmp/app.db'

# إنشاء قاعدة البيانات إذا لم تكن موجودة
with app.app_context():
    from app.models.relational_models import db, init_governorates
    try:
        db.create_all()
        init_governorates(db.session)
        logging.info("تم إنشاء قاعدة البيانات بنجاح")
    except Exception as e:
        logging.error(f"خطأ في إنشاء قاعدة البيانات: {e}")

# تصدير التطبيق للاستخدام مع WSGI
application = app

if __name__ == "__main__":
    # للاختبار المحلي
    app.run(debug=False, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
