@echo off
chcp 65001 >nul
echo 🚀 مرحباً بك في سكريپت نشر نظام مراقبة الإعلام
echo ==================================================
echo.

REM التحقق من وجود ملف requirements.txt
if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo اختر منصة النشر:
echo 1^) Firebase Functions + Hosting
echo 2^) Google App Engine  
echo 3^) Heroku
echo 4^) اختبار محلي
echo.
set /p choice="أدخل رقم الخيار (1-4): "

if "%choice%"=="1" goto firebase
if "%choice%"=="2" goto appengine
if "%choice%"=="3" goto heroku
if "%choice%"=="4" goto local
goto invalid

:firebase
echo 🔄 جاري النشر على Firebase...
echo.

REM التحقق من تسجيل الدخول
firebase projects:list >nul 2>&1
if errorlevel 1 (
    echo 🔑 يرجى تسجيل الدخول إلى Firebase
    firebase login
)

REM التحقق من ربط المشروع
if not exist ".firebaserc" (
    echo 🔗 يرجى ربط المشروع بـ Firebase
    firebase use --add
)

REM النشر
echo 📦 جاري نشر Functions و Hosting...
firebase deploy

if errorlevel 1 (
    echo ❌ فشل النشر على Firebase
) else (
    echo ✅ تم النشر بنجاح على Firebase!
    echo 🌐 يمكنك الوصول للموقع من خلال الرابط المعروض أعلاه
)
goto end

:appengine
echo 🔄 جاري النشر على Google App Engine...
echo.

REM التحقق من تسجيل الدخول
gcloud auth list --filter=status:ACTIVE --format="value(account)" >nul 2>&1
if errorlevel 1 (
    echo 🔑 يرجى تسجيل الدخول إلى Google Cloud
    gcloud auth login
)

REM النشر
echo 📦 جاري النشر على App Engine...
gcloud app deploy

if errorlevel 1 (
    echo ❌ فشل النشر على App Engine
) else (
    echo ✅ تم النشر بنجاح على App Engine!
    echo 🌐 جاري فتح الموقع...
    gcloud app browse
)
goto end

:heroku
echo 🔄 جاري النشر على Heroku...
echo.

REM التحقق من تسجيل الدخول
heroku auth:whoami >nul 2>&1
if errorlevel 1 (
    echo 🔑 يرجى تسجيل الدخول إلى Heroku
    heroku login
)

REM تكوين متغيرات البيئة
echo 🔧 تكوين متغيرات البيئة...
heroku config:set FLASK_ENV=production

REM النشر
echo 📦 جاري النشر على Heroku...
git add .
git commit -m "Deploy to Heroku"
git push heroku main

if errorlevel 1 (
    echo ❌ فشل النشر على Heroku
) else (
    echo ✅ تم النشر بنجاح على Heroku!
    echo 🌐 جاري فتح الموقع...
    heroku open
)
goto end

:local
echo 🔄 جاري تشغيل الاختبار المحلي...
echo.

REM تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt

REM تشغيل التطبيق
echo 🌐 تشغيل التطبيق على http://127.0.0.1:5000
python wsgi.py
goto end

:invalid
echo ❌ خيار غير صحيح
goto end

:end
echo.
echo ==================================================
echo 🎉 انتهت عملية النشر
echo 📝 للمزيد من المعلومات، راجع README_FIREBASE.md
echo.
pause
