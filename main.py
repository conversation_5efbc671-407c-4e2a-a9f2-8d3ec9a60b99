#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نقطة الدخول الرئيسية لنظام مراقبة الإعلام على Firebase Functions
"""

import os
import sys
import warnings
import urllib3
from functions_framework import create_app

# تعطيل تحذيرات urllib3 للاتصالات غير الآمنة
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# إضافة المجلد الحالي إلى مسار البحث
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# استيراد التطبيق
from app import app

# تكوين التطبيق للإنتاج
app.config['DEBUG'] = False
app.config['TESTING'] = False

# تصدير التطبيق للاستخدام مع Firebase Functions
def main(request):
    """
    نقطة الدخول الرئيسية لـ Firebase Functions
    """
    with app.app_context():
        return app.full_dispatch_request()

# للاختبار المحلي
if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=5000)
