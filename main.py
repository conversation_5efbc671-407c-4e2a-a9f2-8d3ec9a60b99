#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نقطة الدخول الرئيسية لنظام مراقبة الإعلام على Firebase Functions
"""

import os
import sys
import warnings
import urllib3
import functions_framework

# تعطيل تحذيرات urllib3 للاتصالات غير الآمنة
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# إضافة المجلد الحالي إلى مسار البحث
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# استيراد التطبيق
from app import app

# تكوين التطبيق للإنتاج على Firebase
app.config['DEBUG'] = False
app.config['TESTING'] = False

# تكوين قاعدة البيانات للإنتاج (استخدام Firestore أو Cloud SQL)
if os.environ.get('GOOGLE_CLOUD_PROJECT'):
    # في بيئة الإنتاج على Firebase
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///tmp/app.db'
else:
    # في البيئة المحلية
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///relational_app.db'

@functions_framework.http
def main(request):
    """
    نقطة الدخول الرئيسية لـ Firebase Functions
    """
    with app.app_context():
        return app.full_dispatch_request()

# للاختبار المحلي
if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=5000)
