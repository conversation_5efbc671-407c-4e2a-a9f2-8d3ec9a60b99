{"functions": [{"source": ".", "codebase": "default", "ignore": ["venv", ".git", "firebase.json", "**/.*", "**/node_modules/**", "**/__pycache__/**", "**/venv/**", "**/*.pyc", "**/backups/**", "**/installer/**"]}], "hosting": {"public": "app/static", "ignore": ["firebase.json", "**/.*", "**/node_modules/**", "**/__pycache__/**", "**/venv/**", "**/*.pyc"], "rewrites": [{"source": "/static/**", "destination": "/static/**"}, {"source": "**", "function": {"functionId": "main", "region": "us-central1"}}], "headers": [{"source": "/static/**", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(css|js)", "headers": [{"key": "Cache-Control", "value": "max-age=86400"}]}, {"source": "**/*.@(png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}]}}