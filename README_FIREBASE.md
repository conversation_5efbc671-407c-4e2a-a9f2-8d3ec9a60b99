# دليل استضافة نظام مراقبة الإعلام على Firebase

## نظرة عامة
هذا الدليل يوضح كيفية استضافة نظام مراقبة الإعلام على منصة Firebase من Google مجاناً.

## المتطلبات الأساسية

### 1. إن<PERSON><PERSON><PERSON> حساب Google
- اذهب إلى [Google Account](https://accounts.google.com)
- أنشئ حساب جديد أو استخدم حساب موجود

### 2. تثبيت Node.js
- اذهب إلى [Node.js](https://nodejs.org)
- حمل وثبت أحدث إصدار LTS

### 3. تثبيت Firebase CLI
```bash
npm install -g firebase-tools
```

### 4. تثبيت Python (إذا لم يكن مثبتاً)
- اذهب إلى [Python.org](https://www.python.org/downloads/)
- حمل وثبت Python 3.8 أو أحدث
- تأكد من إضافة Python إلى PATH

### 5. تثبيت Git (اختياري لكن مُوصى به)
- اذهب إلى [Git](https://git-scm.com/downloads)
- حمل وثبت Git
- هذا مفيد لإدارة الإصدارات

## خطوات إنشاء مشروع Firebase

### الخطوة 1: إنشاء مشروع جديد
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. انقر على "إنشاء مشروع" أو "Create a project"
3. أدخل اسم المشروع (مثل: news-monitoring-system)
4. اختر ما إذا كنت تريد تفعيل Google Analytics (اختياري)
5. انقر على "إنشاء مشروع"

### الخطوة 2: تفعيل الخدمات المطلوبة

#### تفعيل Firebase Functions
1. في لوحة تحكم Firebase، اذهب إلى "Functions"
2. انقر على "البدء" أو "Get started"
3. اختر خطة "Blaze" (الدفع حسب الاستخدام) - مجانية حتى حدود معينة

#### تفعيل Firebase Hosting
1. اذهب إلى "Hosting"
2. انقر على "البدء" أو "Get started"
3. اتبع التعليمات

#### تفعيل Firestore Database (اختياري)
1. اذهب إلى "Firestore Database"
2. انقر على "إنشاء قاعدة بيانات"
3. اختر وضع الاختبار للبداية

## تكوين المشروع المحلي

### الخطوة 0: تحضير البيئة المحلية

#### إنشاء بيئة افتراضية (مُوصى به بشدة)
```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# على Windows:
venv\Scripts\activate

# على macOS/Linux:
source venv/bin/activate
```

#### تثبيت متطلبات Python
```bash
# تأكد من أن pip محدث
python -m pip install --upgrade pip

# تثبيت جميع المتطلبات
pip install -r requirements.txt

# إذا واجهت مشاكل مع بعض المكتبات، ثبتها منفصلة:
pip install flask==3.1.1
pip install flask-sqlalchemy==3.1.1
pip install functions-framework==3.*
pip install gunicorn==20.1.0
```

#### تحضير قاعدة البيانات
```bash
# إنشاء قاعدة البيانات المحلية
python -c "from app import app, db; app.app_context().push(); db.create_all()"
```

### الخطوة 1: تسجيل الدخول إلى Firebase
```bash
firebase login
```

### الخطوة 2: تهيئة المشروع
```bash
firebase init
```

اختر:
- Functions: Configure a Cloud Functions directory
- Hosting: Configure files for Firebase Hosting

### الخطوة 3: ربط المشروع
```bash
firebase use --add
```
اختر المشروع الذي أنشأته واعطه اسماً مستعاراً (مثل: default)

### الخطوة 4: تحديث ملف .firebaserc
```json
{
  "projects": {
    "default": "your-actual-project-id"
  }
}
```

## نشر التطبيق

### الخطوة 1: تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### الخطوة 2: نشر Functions و Hosting
```bash
firebase deploy
```

### أو نشر كل خدمة منفصلة:
```bash
# نشر Functions فقط
firebase deploy --only functions

# نشر Hosting فقط
firebase deploy --only hosting
```

## الحدود المجانية لـ Firebase

### Firebase Functions (خطة Spark - مجانية)
- 2 مليون استدعاء شهرياً
- 400,000 ثانية GB من وقت الحوسبة
- 5 GB من حركة البيانات الصادرة

### Firebase Hosting
- 10 GB من التخزين
- 10 GB من النقل الشهري
- شهادة SSL مجانية
- CDN عالمي

### Firestore Database
- 1 GB من التخزين
- 50,000 قراءة يومياً
- 20,000 كتابة يومياً
- 20,000 حذف يومياً

## نصائح للحفاظ على الاستخدام المجاني

1. **مراقبة الاستخدام**: تحقق من لوحة التحكم بانتظام
2. **تحسين الكود**: قلل من عدد استدعاءات قاعدة البيانات
3. **استخدام التخزين المؤقت**: لتقليل الطلبات المتكررة
4. **ضغط الملفات**: لتقليل استخدام النقل

## استكشاف الأخطاء

### مشكلة: فشل في النشر
```bash
# تحقق من السجلات
firebase functions:log

# إعادة النشر
firebase deploy --only functions --debug
```

### مشكلة: خطأ في قاعدة البيانات
- تأكد من أن قاعدة البيانات SQLite موجودة
- تحقق من صلاحيات الملفات

### مشكلة: خطأ في الاستيراد
- تأكد من أن جميع المتطلبات مثبتة
- تحقق من مسارات الملفات

## روابط مفيدة

- [Firebase Console](https://console.firebase.google.com)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Firebase Functions Python](https://firebase.google.com/docs/functions/python)
- [Firebase Hosting](https://firebase.google.com/docs/hosting)

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من السجلات في Firebase Console
2. راجع الوثائق الرسمية
3. ابحث في Stack Overflow
4. اطلب المساعدة في مجتمع Firebase
